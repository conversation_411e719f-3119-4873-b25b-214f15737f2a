package system

import (
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	systemRes "github.com/flipped-aurora/gin-vue-admin/server/model/system/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AuthProviderApi struct {
	BaseApi // 组合BaseApi以便使用通用方法
}

// UnifiedLogin 统一登录API
// @Tags      AuthProvider
// @Summary   统一登录接口
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.UnifiedLoginRequest                                      true  "统一登录请求"
// @Success   200   {object}  response.Response{data=systemRes.UnifiedLoginResponse,msg=string}  "统一登录成功"
// @Router    /base/unified-login [post]
func (ap *AuthProviderApi) UnifiedLogin(c *gin.Context) {
	var req systemReq.UnifiedLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	//if req.Provider == system.ProviderTypeUsername || req.Provider == system.ProviderTypePhone {
	//	// 易盾验证码验证
	//	if !ap.verifyYidunCaptchaWithRequest(c, req.Validate) {
	//		return
	//	}
	//}

	// 统一登录
	loginResp, err := authProviderService.UnifiedLogin(req)
	if err != nil {
		global.GVA_LOG.Error("统一登录失败!", zap.Error(err))
		clientIP := c.ClientIP()
		ap.BaseApi.incrementFailCount(clientIP)
		response.FailWithMessage("登录失败: "+err.Error(), c)
		return
	}

	ap.handleSuccessfulLogin(c, loginResp)
}

// BindProvider 绑定认证提供商
// @Tags      AuthProvider
// @Summary   绑定认证提供商
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.AuthProviderBindRequest                                 true  "绑定认证提供商请求"
// @Success   200   {object}  response.Response{data=systemRes.BindProviderResponse,msg=string}  "绑定成功"
// @Router    /auth/bind-provider [post]
func (ap *AuthProviderApi) BindProvider(c *gin.Context) {
	var req systemReq.AuthProviderBindRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userId := utils.GetUserID(c)
	if userId == 0 {
		response.FailWithMessage("未获取到用户信息", c)
		return
	}

	err := authProviderService.BindProvider(userId, req)
	if err != nil {
		global.GVA_LOG.Error("绑定认证提供商失败!", zap.Error(err))
		response.FailWithMessage("绑定失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(systemRes.BindProviderResponse{
		Success:  true,
		Provider: req.Provider,
		Message:  "绑定成功",
	}, "绑定认证提供商成功", c)
}

// UnbindProvider 解绑认证提供商
// @Tags      AuthProvider
// @Summary   解绑认证提供商
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.AuthProviderUnbindRequest                                true  "解绑认证提供商请求"
// @Success   200   {object}  response.Response{data=systemRes.UnbindProviderResponse,msg=string}  "解绑成功"
// @Router    /auth/unbind-provider [post]
func (ap *AuthProviderApi) UnbindProvider(c *gin.Context) {
	var req systemReq.AuthProviderUnbindRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userId := utils.GetUserID(c)
	if userId == 0 {
		response.FailWithMessage("未获取到用户信息", c)
		return
	}

	err := authProviderService.UnbindProvider(userId, req)
	if err != nil {
		global.GVA_LOG.Error("解绑认证提供商失败!", zap.Error(err))
		response.FailWithMessage("解绑失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(systemRes.UnbindProviderResponse{
		Success:  true,
		Provider: req.Provider,
		Message:  "解绑成功",
	}, "解绑认证提供商成功", c)
}

// GetUserProviders 获取用户认证提供商列表
// @Tags      AuthProvider
// @Summary   获取用户认证提供商列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{data=systemRes.UserProvidersResponse,msg=string}  "获取成功"
// @Router    /auth/user-providers [get]
func (ap *AuthProviderApi) GetUserProviders(c *gin.Context) {
	userId := utils.GetUserID(c)
	if userId == 0 {
		response.FailWithMessage("未获取到用户信息", c)
		return
	}

	providers, err := authProviderService.GetUserProviders(userId)
	if err != nil {
		global.GVA_LOG.Error("获取用户认证提供商列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(providers, "获取用户认证提供商列表成功", c)
}

// CheckProviderExist 检查认证提供商是否存在
// @Tags      AuthProvider
// @Summary   检查认证提供商是否存在
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.CheckProviderExistRequest                                 true  "检查认证提供商存在性请求"
// @Success   200   {object}  response.Response{data=systemRes.ProviderExistResponse,msg=string}  "检查成功"
// @Router    /auth/check-provider [post]
func (ap *AuthProviderApi) CheckProviderExist(c *gin.Context) {
	var req systemReq.CheckProviderExistRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	result, err := authProviderService.CheckProviderExist(req)
	if err != nil {
		global.GVA_LOG.Error("检查认证提供商失败!", zap.Error(err))
		response.FailWithMessage("检查失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "检查认证提供商成功", c)
}

// GetSupportedProviders 获取支持的认证提供商列表
// @Tags      AuthProvider
// @Summary   获取支持的认证提供商列表
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{data=map[string]interface{},msg=string}  "获取成功"
// @Router    /auth/supported-providers [get]
func (ap *AuthProviderApi) GetSupportedProviders(c *gin.Context) {
	providers := map[string]interface{}{
		"providers": []map[string]interface{}{
			{
				"type":        system.ProviderTypeUsername,
				"name":        "用户名密码",
				"description": "使用用户名和密码进行登录",
				"enabled":     true,
			},
			{
				"type":        system.ProviderTypeEmail,
				"name":        "邮箱验证码",
				"description": "使用邮箱验证码进行登录",
				"enabled":     true,
			},
			{
				"type":        system.ProviderTypePhone,
				"name":        "手机验证码",
				"description": "使用手机验证码进行登录",
				"enabled":     true,
			},
			{
				"type":        system.ProviderTypeWechat,
				"name":        "微信登录",
				"description": "使用微信账号进行登录",
				"enabled":     true,
			},
			{
				"type":        system.ProviderTypeGithub,
				"name":        "GitHub登录",
				"description": "使用GitHub账号进行登录",
				"enabled":     true,
			},
			{
				"type":        system.ProviderTypeGoogle,
				"name":        "Google登录",
				"description": "使用Google账号进行登录",
				"enabled":     true,
			},
		},
	}

	response.OkWithDetailed(providers, "获取支持的认证提供商列表成功", c)
}

// SendVerificationCode 发送验证码
// @Tags      AuthProvider
// @Summary   发送验证码
// @accept    application/json
// @Produce   application/json
// @Param     data  body      map[string]interface{}                            true  "发送验证码请求"
// @Success   200   {object}  response.Response{msg=string}                     "发送成功"
// @Router    /auth/send-code [post]
func (ap *AuthProviderApi) SendVerificationCode(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	codeType, ok := req["type"].(string)
	if !ok {
		response.FailWithMessage("验证码类型不能为空", c)
		return
	}

	switch codeType {
	case "email":
		email, ok := req["email"].(string)
		if !ok || email == "" {
			response.FailWithMessage("邮箱不能为空", c)
			return
		}
		// TODO: 实现邮箱验证码发送逻辑
		global.GVA_LOG.Info("发送邮箱验证码", zap.String("email", email))
		response.OkWithMessage("邮箱验证码发送成功", c)

	case "phone":
		phone, ok := req["phone"].(string)
		if !ok || phone == "" {
			response.FailWithMessage("手机号不能为空", c)
			return
		}
		// TODO: 实现手机验证码发送逻辑
		global.GVA_LOG.Info("发送手机验证码", zap.String("phone", phone))
		response.OkWithMessage("手机验证码发送成功", c)

	default:
		response.FailWithMessage("不支持的验证码类型", c)
	}
}

// handleSuccessfulLogin 处理登录成功，使用新的会话管理系统
func (ap *AuthProviderApi) handleSuccessfulLogin(c *gin.Context, loginResp *systemRes.UnifiedLoginResponse) {
	user := loginResp.User

	if loginResp.NeedsBind {
		// 需要绑定账号，返回与UnifiedLogin一致的格式
		response.OkWithDetailed(map[string]interface{}{
			"action":    "bind",
			"user":      nil,
			"token":     "",
			"expiresAt": 0,
			"provider":  loginResp.Provider,
			"isNewUser": false,
			"needsBind": true,
			"tempData":  loginResp.TempData,
		}, "需要绑定账号", c)
	} else {
		// 检查用户状态
		if !ap.BaseApi.checkUserStatus(loginResp.User) {
			global.GVA_LOG.Error("登录失败! 用户被禁止登录!")
			clientIP := c.ClientIP()
			ap.BaseApi.incrementFailCount(clientIP)
			response.FailWithMessage("用户被禁止登录", c)
			return
		}
		// 获取设备ID（如果是设备登录）
		deviceID := c.GetHeader("Device-ID")
		if deviceID == "" {
			// 尝试从请求体中获取deviceId
			var loginReq struct {
				DeviceID string `json:"deviceId"`
			}
			c.ShouldBindJSON(&loginReq)
			deviceID = loginReq.DeviceID
		}

		var deviceIDPtr *string
		if deviceID != "" {
			deviceIDPtr = &deviceID
		}

		// 使用新的会话管理系统创建会话和token
		token, err := jwtService.CreateSessionAndToken(&user, deviceIDPtr, c.ClientIP(), c.GetHeader("User-Agent"))
		if err != nil {
			global.GVA_LOG.Error("创建会话和token失败",
				zap.Uint("userId", user.ID),
				zap.String("username", user.Username),
				zap.Any("deviceId", deviceIDPtr),
				zap.Error(err))
			response.FailWithMessage("登录失败: "+err.Error(), c)
			return
		}

		// 解析token获取claims（用于过期时间）
		j := utils.NewJWT()
		claims, err := j.ParseToken(token)
		if err != nil {
			global.GVA_LOG.Error("解析token失败!", zap.Error(err))
			response.FailWithMessage("登录失败: "+err.Error(), c)
			return
		}

		// 检查并发放首次登录奖励
		ap.BaseApi.CheckAndGrantFirstLoginReward(user.ID)

		// 设置cookie中的token
		utils.SetToken(c, token, int(claims.RegisteredClaims.ExpiresAt.Unix()-time.Now().Unix()))

		// 尝试绑定设备（如果有设备ID），绑定失败不影响登录
		if deviceID != "" {
			err = deviceService.BindDevice(deviceID, &user.ID)
			if err != nil {
				// 设备绑定失败，记录日志但不影响登录流程
				global.GVA_LOG.Error("设备绑定失败",
					zap.String("deviceId", deviceID),
					zap.Uint("userId", user.ID),
					zap.Error(err))
			} else {
				global.GVA_LOG.Info("设备绑定成功",
					zap.String("deviceId", deviceID),
					zap.Uint("userId", user.ID))
			}
		}

		response.OkWithDetailed(map[string]interface{}{
			"action":    "login",
			"user":      user,
			"token":     token,
			"expiresAt": claims.RegisteredClaims.ExpiresAt.Unix() * 1000,
			"provider":  loginResp.Provider,
			"isNewUser": loginResp.IsNewUser,
			"tempData":  nil,
		}, "登录成功", c)
	}
}

// MergeAccounts 合并账号
// @Tags      AuthProvider
// @Summary   合并账号
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.AccountMergeRequest                                         true  "账号合并请求"
// @Success   200   {object}  response.Response{data=systemRes.AccountMergeResponse,msg=string}     "合并成功"
// @Router    /auth/merge-accounts [post]
func (ap *AuthProviderApi) MergeAccounts(c *gin.Context) {
	var req systemReq.AccountMergeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	result, err := authProviderService.MergeAccounts(req)
	if err != nil {
		global.GVA_LOG.Error("账号合并失败!", zap.Error(err))
		response.FailWithMessage("合并失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, result.Message, c)
}

// GetAccountsForMerge 获取可合并的账号列表
// @Tags      AuthProvider
// @Summary   获取可合并的账号列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{data=[]systemRes.CandidateUser,msg=string}  "获取成功"
// @Router    /auth/accounts-for-merge [get]
func (ap *AuthProviderApi) GetAccountsForMerge(c *gin.Context) {
	userId := utils.GetUserID(c)
	if userId == 0 {
		response.FailWithMessage("未获取到用户信息", c)
		return
	}

	// 获取当前用户信息
	var currentUser system.SysUser
	if err := global.GVA_DB.Where("id = ?", userId).First(&currentUser).Error; err != nil {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	// 查找可能的重复账号（根据邮箱、手机号）
	var candidateUsers []system.SysUser
	query := global.GVA_DB.Model(&system.SysUser{}).Where("id != ?", userId)

	conditions := []string{}
	args := []interface{}{}

	if currentUser.Email != "" {
		conditions = append(conditions, "email = ?")
		args = append(args, currentUser.Email)
	}
	if currentUser.Phone != "" {
		conditions = append(conditions, "phone = ?")
		args = append(args, currentUser.Phone)
	}

	if len(conditions) > 0 {
		whereClause := strings.Join(conditions, " OR ")
		if err := query.Where(whereClause, args...).Find(&candidateUsers).Error; err != nil {
			response.FailWithMessage("查询候选账号失败", c)
			return
		}
	}

	// 构建候选用户列表
	var candidates []systemRes.CandidateUser
	for _, user := range candidateUsers {
		matchBy := []string{}
		if currentUser.Email != "" && user.Email == currentUser.Email {
			matchBy = append(matchBy, "email")
		}
		if currentUser.Phone != "" && user.Phone == currentUser.Phone {
			matchBy = append(matchBy, "phone")
		}

		// 脱敏处理
		maskedEmail := user.Email
		if maskedEmail != "" {
			parts := strings.Split(maskedEmail, "@")
			if len(parts) == 2 && len(parts[0]) > 2 {
				maskedEmail = parts[0][:2] + "***@" + parts[1]
			}
		}

		maskedPhone := user.Phone
		if maskedPhone != "" && len(maskedPhone) >= 7 {
			maskedPhone = maskedPhone[:3] + "****" + maskedPhone[len(maskedPhone)-4:]
		}

		candidate := systemRes.CandidateUser{
			ID:       user.ID,
			Username: user.Username,
			NickName: user.NickName,
			Email:    maskedEmail,
			Phone:    maskedPhone,
			Avatar:   user.HeaderImg,
			MatchBy:  strings.Join(matchBy, ","),
		}
		candidates = append(candidates, candidate)
	}

	response.OkWithDetailed(candidates, "获取可合并账号列表成功", c)
}

// 绑定手机号接口
func (ap *AuthProviderApi) BindWechatPhone(c *gin.Context) {
	var req systemReq.WechatBindPhoneRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取设备信息和客户端信息
	deviceID := c.GetHeader("Device-ID")
	var deviceIDPtr *string
	if deviceID != "" {
		deviceIDPtr = &deviceID
	}

	result, err := authProviderService.BindWechatPhone(req, deviceIDPtr, c.ClientIP(), c.GetHeader("User-Agent"))
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 尝试绑定设备（如果有设备ID），绑定失败不影响登录
	if deviceID != "" {
		err = deviceService.BindDevice(deviceID, &result.User.ID)
		if err != nil {
			// 设备绑定失败，记录日志但不影响登录流程
			global.GVA_LOG.Error("设备绑定失败",
				zap.String("deviceId", deviceID),
				zap.Uint("userId", result.User.ID),
				zap.Error(err))
		} else {
			global.GVA_LOG.Info("设备绑定成功",
				zap.String("deviceId", deviceID),
				zap.Uint("userId", result.User.ID))
		}
	}

	response.OkWithDetailed(result, "绑定成功", c)
}
